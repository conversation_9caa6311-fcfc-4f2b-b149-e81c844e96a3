#!/usr/bin/env python3
"""
Test script to run sample_template2.json with Runway API integration
"""

import asyncio
import os
import sys
from pathlib import Path
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from unified_video_processor import UnifiedVideoProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sample2_runway_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

async def test_sample2_with_runway():
    """Test processing sample_template2.json with Runway API"""
    
    logger.info("🚀 Testing sample_template2.json with Runway API")
    logger.info("=" * 60)
    
    # Check if Runway API key is available
    runway_api_key = os.getenv('RUNWAY_API_KEY')
    if not runway_api_key or runway_api_key == 'your_runway_api_key_here':
        logger.warning("⚠️  Runway API key not set or is placeholder. Speech generation will be skipped.")
        logger.info("To enable Runway speech generation, set RUNWAY_API_KEY in your .env file")
    else:
        logger.info("✅ Runway API key found")
    
    # Initialize processor
    processor = UnifiedVideoProcessor(
        images_dir=r"C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140",
        runway_api_key=runway_api_key,
        output_dir="sample2_runway_output"
    )
    
    # Template path
    template_path = "sample_template2.json"
    
    if not Path(template_path).exists():
        logger.error(f"❌ Template not found: {template_path}")
        return False
    
    try:
        logger.info(f"📁 Processing template: {template_path}")
        logger.info(f"🎬 Output directory: sample2_runway_output")
        
        # Process the template
        output_path = await processor.process_template(template_path)
        
        logger.info("=" * 60)
        logger.info("✅ SUCCESS!")
        logger.info(f"📹 Video created: {output_path}")
        logger.info(f"📊 Check sample2_runway_test.log for detailed logs")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error("=" * 60)
        logger.error("❌ FAILED!")
        logger.error(f"💥 Error: {e}")
        logger.error(f"📊 Check sample2_runway_test.log for detailed error information")
        logger.error("=" * 60)
        return False
        
    finally:
        # Cleanup
        processor.cleanup()

async def main():
    """Main function"""
    
    # Print environment info
    logger.info("🔧 Environment Information:")
    logger.info(f"   - Python: {sys.version}")
    logger.info(f"   - Working Directory: {os.getcwd()}")
    logger.info(f"   - Runway API Key: {'✅ Set' if os.getenv('RUNWAY_API_KEY') and os.getenv('RUNWAY_API_KEY') != 'your_runway_api_key_here' else '❌ Not set'}")
    logger.info("")
    
    # Run the test
    success = await test_sample2_with_runway()
    
    if success:
        logger.info("🎉 Test completed successfully!")
        sys.exit(0)
    else:
        logger.info("💥 Test failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
