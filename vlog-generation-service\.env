# Database Configuration
DB_USER=root
DB_PASSWORD=@Oppa121089
DB_HOST=localhost
DB_DATABASE=vlog_generator

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# API Configuration
API_BASE_URL=http://localhost:5000

# External API Keys
ANTHROPIC_API_KEY=************************************************************************************************************
XAI_API_KEY=************************************************************************************
RUNWARE_API_KEY=350ObzN0b54Wn4cHgID0PnNxutW0jNiq
ELEVENLABS_API_KEY=***************************************************
RUNWAY_API_KEY=your_runway_api_key_here
FISHAUDIO_API_KEY=8449d93d3ac143eb9dc05382ae0514da
DEEKSEEK_API_KEY=sk-ece0bc75827542b4a6d64ea9b6f0cbaf
GEMINI_API_KEY=AIzaSyCeynP-7LSbQ-EiSKidN29JskIKTsLWynY

# Image generation parameters
IMAGE_MODEL=runware:101@1
IMAGE_HEIGHT=2048
IMAGE_WIDTH=1152
IMAGE_CFG_SCALE=7.5
IMAGE_NEGATIVE_PROMPT=blurry, low quality, deformed, text, watermark

