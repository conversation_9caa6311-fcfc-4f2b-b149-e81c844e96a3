�

    ��5h	�  �                   ��  � d Z ddlZddlZddlZddlZddlZddlZddlZddlZ	 ddl	Z	dZ
ddl
mZ ddlmZmZmZmZmZmZ ddlmZ ddlZdd	lmZmZmZ ddlZ	 dd
lmZm Z m!Z!m"Z"m#Z#m$Z$m%Z%  ed�        ej\                  ej^                  d ej`                  d�       ejb                  �       g��        ejd                  e3�      Z4 G d� d�      Z5d de5de6fd�Z7d� Z8e3dk(  r ejr                   e8�       �       yy# e$ r
 dZ
 ed�       Y ��w xY w# e$ r^ 	 ddl&mZ dd
l'm(Z( ddl)m Z m!Z! ddl*m"Z" ddl+m#Z# ddl,m$Z$ ddl)m%Z%  ed�       n%# e$ r  ed�        ejZ                  d�       Y nw xY wY ��w xY w)!z�
Unified Video Processor for VisionFrame AI
Handles all video templates with automatic image sourcing and Runway API speech generation
�    NTFuM   ⚠️  aiohttp not available. Runway API speech generation will be disabled.)�Path)�Dict�List�Optional�Tuple�Any�Union)�datetime)�Image�	ImageDraw�	ImageFont)�
VideoFileClip�	ImageClip�TextClip�CompositeVideoClip�
AudioFileClip�concatenate_videoclips�	ColorClipu   ✅ Using MoviePy 1.x imports)r   )�ImageSequenceClip)r   r   )r   )r   )r   )r   u    ✅ Using MoviePy legacy importsu1   ❌ MoviePy not available. Please install moviepy�   z)%(asctime)s - %(levelname)s - %(message)szunified_video_processing.log)�level�format�handlersc                   �  � e Zd ZdZ	 	 	 d<dededefd�Zdee   fd�Zde	e   fd	�Z
d
ede	e   fd�Zd=ded
ede	e   fd�Z
dede	e   fd�Zdedeeeef   fd�Zdedeeeef   fd�Zdedededefd�Zdedededefd�Zd>dededefd�Zdedefd �Zdedefd!�Zd"ee   fd#�Zd$efd%�Zd&efd'�Zd(ed)ed*ede	e   fd+�Zd,ee   d)ed*ed-edef
d.�Zd/ede	e   fd0�Z d1e!d2ee   d)ed*ede!f
d3�Z"d&ed)ed*ed-ede	e#   f
d4�Z$d1e#d2ee   de#fd5�Z%d6ed)ed*ed-ede	e!   f
d7�Z&d6ed)ed*ed8ede	e'jP                     f
d9�Z)d$ede	e*   fd:�Z+d;� Z,y)?�UnifiedVideoProcessora  
    Unified video processor that handles all template types with:
    - Automatic image sourcing from specified directory
    - Runway API for speech generation
    - Template-agnostic processing
    - Comprehensive element support (text, image, shape, audio, composition)
    N�
images_dir�runway_api_key�
output_dirc                 �  � t        |�      | _        |xs t        j                  d�      | _        t        |�      | _        | j
                  j
                  d��       t        t        j                  �       �      dz  | _	        | j                  j
                  d��       i | _
        i | _        d| _        d| _
        d| _        | j                  �       | _        t"        j%                  d�       t"        j%                  d	| j                  � ��       t"        j%                  d
t'        | j                   �      � ��       t"        j%                  d| j
                  � ��       y)
z�
        Initialize the unified video processor

        Args:
            images_dir: Directory containing images for templates
            runway_api_key: API key for Runway speech generation
            output_dir: Directory for output videos
        �RUNWAY_API_KEYT)�exist_ok�unified_video_processor�   �#ffffff�#000000z#Unified Video Processor initializedzImages directory: zAvailable images: zOutput directory: N)r   r   �os�getenvr   r   �mkdir�tempfile�
gettempdir�temp_dir�image_cache�
font_cache�default_font_size�default_font_color�default_background_color�_load_available_images�available_images�logger�info�len)�selfr   r   r   s       ��C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\videoprocessor\unified_video_processor.py�__init__zUnifiedVideoProcessor.__init__E   s  � � �z�*���,�K��	�	�:J�0K����z�*��������t��,� �X�0�0�2�3�6O�O��
��
�
���T��*� ������ "$���"+���(1��%� !%� ;� ;� =������9�;����(����(9�:�;����(��T�-B�-B�)C�(D�E�F����(����(9�:�;�    �returnc                 �  � | j                   j                  �       s$t        j                  d| j                   � ��       g S h d�}g }| j                   j	                  �       D ]A  }|j                  �       s�|j                  j                  �       |v s�1|j                  |�       �C t        j                  dt        |�      � d| j                   � ��       |S )z7Load list of available images from the images directoryzImages directory not found: >   �.tiff�.webp�.bmp�.jpg�.png�.jpegzFound z images in )r   �existsr3   �warning�iterdir�is_file�suffix�lower�appendr4   r5   )r6   �image_extensions�images�	file_paths       r7   r1   z,UnifiedVideoProcessor._load_available_imagesk   s�   � ����%�%�'��N�N�9�$�/�/�9J�K�L��I�N�������0�0�2� 	)�I�� � �"�y�'7�'7�'=�'=�'?�CS�'S��
�
�i�(�	)� 	���f�S��[�M��T�_�_�4E�F�G��
r9   c                 �   � | j                   st        j                  d�       yt        j                  | j                   �      S )z,Get a random image from the available imageszNo images availableN)r2   r3   rC   �random�choice)r6   s    r7   �get_random_imagez&UnifiedVideoProcessor.get_random_image{   s0   � ��$�$��N�N�0�1���}�}�T�2�2�3�3r9   �indexc                 �   � | j                   r|t        | j                   �      k\  r| j                  �       S | j                   |   S )z(Get image by index from available images)r2   r5   rO   )r6   rP   s     r7   �get_image_by_indexz(UnifiedVideoProcessor.get_image_by_index�   s=   � ��$�$���T�5J�5J�1K�(K��(�(�*�*��$�$�U�+�+r9   �text�voice_idc              �   ��  K  � | j                   st        j                  d�       yt        st        j                  d�       y	 t	        j
                  �       j
                  d�      }d|� d�}| j                  |z  }d}d| j                   � �d	d
�}||ddd
�}t        j                  d|dd � d��       t        j                  �       4 �d{  ��� }	|	j                  |||��      4 �d{  ��� }
|
j                  dk(  r�|
j                  �       � d{  ��� }t        |d�      5 }|j                  |�       ddd�       t        j                  d|� ��       t!        |�      cddd�      �d{  ���  cddd�      �d{  ���  S |
j#                  �       � d{  ��� }
t        j%                  d|
j                  � d|
� ��       	 ddd�      �d{  ���  ddd�      �d{  ���  y7 ��7 ��7 ��# 1 sw Y   ��xY w7 ��7 �y7 �b7 �.7 � # 1 �d{  ���7  sw Y   nxY wddd�      �d{  ���7   y# 1 �d{  ���7  sw Y   yxY w# t&        $ r"}t        j%                  d|� ��       Y d}~yd}~ww xY w�w)z�
        Generate speech using Runway API

        Args:
            text: Text to convert to speech
            voice_id: Voice ID for speech generation

        Returns:
            Path to generated audio file or None if failed
        z7Runway API key not provided, skipping speech generationNz,aiohttp not available, cannot use Runway API�
%Y%m%d_%H%M%S�speech_�.mp3z+https://api.runwayml.com/v1/speech/generatezBearer zapplication/json)�
AuthorizationzContent-Type�mp3�      �?)rS   �voicer   �speedzGenerating speech for text: �2   �...)�headers�json��   �wbzSpeech generated successfully: zRunway API error: z - z)Error generating speech with Runway API: )r   r3   rC   �AIOHTTP_AVAILABLEr
   �now�strftimer+   r4   �aiohttp�
ClientSession�post�status�read�open�write�strrS   �error�	Exception)r6   rS   rT   �	timestamp�audio_filename�
audio_path�urlr`   �payload�session�response�
audio_data�f�
error_text�es                  r7   �generate_speech_runwayz,UnifiedVideoProcessor.generate_speech_runway�   s>  � �� � �"�"��N�N�T�U�� ��N�N�I�J��&	� ����/�/��@�I�&�y�k��6�N�����7�J� @�C� $+�4�+>�+>�*?�!@� 2��G� �!���	�G� 
�K�K�6�t�C�R�y�k��E�F��,�,�.� 
$� 
$�'�"�<�<��W�7�<�K� 
$� 
$�x����#�-�+3�=�=�?�%:�
�!�*�d�3� 0�q��G�G�J�/�0����&E�j�\�$R�S�"�:��
$� 
$� 
$�
$� 
$� 
$� ,4�=�=�?�%:�
����'9�(�/�/�9J�#�j�\�%Z�[�#�
$� 
$�
$� 
$� 
$��
$��%:��0� 0��
$��
$�� &;��
$��
$��
$� 
$� 
$��
$� 
$� 
$� 
$� 
$�� � 	��L�L�D�Q�C�H�I���	�s\  �?I#�BH5 �	G�
H5 �
H �&G�'H �*#G9�
G!�G9�G#�0*G9�H �&G/�'H �+H5 �7G1�8H5 �<I#�=G9�G3�*G9�;H �G5�H �H5 �G7�H5 �I#�H5 �H �!G9�#G,�(G9�/H �1H5 �3G9�5H �7H5 �9H	�?H� H	�H �H5 �H�H5 �I#� H2�&H)�'H2�.H5 �1I#�2H5 �5	I �>I�I#�I � I#c                 �  � 	 t        j                  �       j                  d�      }d|� d�}| j                  |z  }t	        dt        |j
                  �       �      dz  �      }t        d�      j                  |�      }t        j                  d|dd � d	��       y# t        $ r"}t        j                  d
|� ��       Y d}~yd}~ww xY w)z�
        Fallback speech generation using system TTS or placeholder

        Args:
            text: Text to convert to speech

        Returns:
            Path to generated audio file or None
        rV   �fallback_speech_rX   �      @g      �?Nz!Using fallback silent audio for: r^   r_   z%Error in fallback speech generation: )
r
   re   rf   r+   �maxr5   �splitr   �set_durationr3   rC   rp   ro   )r6   rS   rq   rr   rs   �duration�silent_clipr{   s           r7   �generate_speech_fallbackz.UnifiedVideoProcessor.generate_speech_fallback�   s�   � �	� !����/�/��@�I�/�	�{�$�?�N�����7�J� �3��D�J�J�L� 1�C� 7�8�H�'��-�:�:�8�D�K� 
�N�N�>�t�C�R�y�k��M�N���� 	��L�L�@���D�E���	�s   �BB �	C� B=�=C�	color_strc                 �,  � |syt        |�      j                  �       }|j                  d�      r�|j                  dd�      j                  dd�      }|j	                  d�      D �cg c]  }|j                  �       �� }}t        |�      dk\  r/t
        |d   �      t
        |d   �      t
        |d	   �      }}}|||fS y|j                  d
�      r| j                  |�      S 	 | j                  d
|z   �      S c c}w #  t        j                  d|� d��       Y yxY w)
z-Parse color string (hex or rgba) to RGB tuple���   r�   r�   zrgba(� �)�,�   r   r   �   �#zCould not parse color 'z', using white)
rn   �strip�
startswith�replacer�   r5   �int�
hex_to_rgbr3   rC   )r6   r�   �rgba_str�v�values�r�g�bs           r7   �parse_colorz!UnifiedVideoProcessor.parse_color�   s  � ��"��	�N�(�(�*�	�����(� �(�(��"�5�=�=�c�2�F�H�)1����)<�=�A�a�g�g�i�=�F�=��6�{�a���f�Q�i�.�#�f�Q�i�.�#�f�Q�i�.�a�1���1�a�y� � � �
!�
!�#�
&��?�?�9�-�-�
'����s�Y��7�7�� >��
'����!8���>�R�S�&�s   �#C1�C6 �6D�	hex_colorc                 �l   �� �j                  d�      �t        ��      dk(  rt        �fd�dD �       �      S y)zConvert hex color to RGB tupler�   �   c              3   �B   �K  � | ]  }t        �||d z    d�      �� � y�w)r�   �   N)r�   )�.0�ir�   s     �r7   �	<genexpr>z3UnifiedVideoProcessor.hex_to_rgb.<locals>.<genexpr>�   s#   �� �� �F�q��Y�q��1��-�r�2�F�s   �)r   r�   �   r�   )�lstripr5   �tuple)r6   r�   s    `r7   r�   z UnifiedVideoProcessor.hex_to_rgb�   s3   �� ��$�$�S�)�	��y�>�Q���F�I�F�F�F�r9   �
dimension_str�canvas_width�
canvas_heightc                 �  � t        |t        t        f�      rt        |�      S t        |�      j	                  �       j                  �       }d|v rFt        |j
                  dd�      j	                  �       �      }t        ||�      }t        ||z  dz  �      S d|v r4t        |j
                  dd�      j	                  �       �      }t        |�      S d|v r:t        |j
                  dd�      j	                  �       �      }t        ||z  dz  �      S 	 t        t        |�      �      S # t        $ r t        j                  d|� d��       Y yw xY w)z9Parse dimension string (vmin, px, %, etc.) to pixel value�vminr�   �d   �px�%zCould not parse dimension 'z', using default 100px)�
isinstancer�   �floatrn   r�   rG   r�   �min�
ValueErrorr3   rC   )r6   r�   r�   r�   �value�
min_dimensions         r7   �parse_dimensionz%UnifiedVideoProcessor.parse_dimension  s-  � ��m�c�5�\�2��}�%�%��M�*�0�0�2�8�8�:�
��]�"��-�/�/���;�A�A�C�D�E���m�<�M��}�u�,�s�2�3�3�
�]�
"��-�/�/��b�9�?�?�A�B�E��u�:��
�M�
!��-�/�/��R�8�>�>�@�A�E��|�e�+�c�1�2�2�
��5��/�0�0��� 
����!<�]�O�Ka�b�c��
�s   �
D �"E�E�position_str�canvas_dimension�element_dimensionc                 �  � t        |t        t        f�      rt        |�      S t        |�      j	                  �       j                  �       }d|v r@t        |j
                  dd�      j	                  �       �      }t        ||z  dz  �      |dz  z
  S d|v r4t        |j
                  dd�      j	                  �       �      }t        |�      S d|v r:t        |j
                  dd�      j	                  �       �      }t        ||z  dz  �      S 	 t        t        |�      �      S # t        $ r' t        j                  d|� d��       |dz  |dz  z
  cY S w xY w)	z$Parse position string to pixel valuer�   r�   r�   r�   r�   r�   zCould not parse position 'z', using center)
r�   r�   r�   rn   r�   rG   r�   r�   r3   rC   )r6   r�   r�   r�   r�   s        r7   �parse_positionz$UnifiedVideoProcessor.parse_position  sC  � ��l�S�%�L�1��|�$�$��<�(�.�.�0�6�6�8���,���,�.�.�s�B�7�=�=�?�@�E��'�%�/�#�5�6�9J�a�9O�O�O�
�\�
!��,�.�.�t�R�8�>�>�@�A�E��u�:��
�|�
#��,�.�.�v�r�:�@�@�B�C�E��'�%�/�#�5�6�6�
F��5��.�/�/��� 
F����!;�L�>��Y�Z�'�1�,�/@�A�/E�E�E�
F�s   �D �-E�E�
template_path�output_filenamec           
   �   �>  K  � t         j                  d|� ��       	 t        |dd��      5 }t        j                  |�      }ddd�       | j                  �      st        d�      �|j                  dd	�      }|j                  d
d�      }|j                  dd
�      }|st        |�      j                  }	|	� d|� �}| j                  |z  }
| j                  |�      � d{  ��� }g }d}
|j                  dg �      D ]�  }|j                  d�      dk(  s�| j                  |||�      � d{  ��� }|s�6|j                  |�       |
|j                   z
  }
t         j                  d|j                  dd�      � d|j                   d�d��       �� |st        d�      �t         j                  dt#        |�      � d��       t%        |d��      }t         j                  d|
� d��       |j'                  t)        |
�      d d!d"d#d$d%d�&�       |j+                  �        |D ]  }|j+                  �        � t         j                  d'|
� ��       t         j                  d(|
d�d��       t)        |
�      S # 1 sw Y   ��"xY w# t
        $ r}t         j
                  d|� ��       � d}~ww xY w7 ���7 ��v�w))z�
        Process a complete video template

        Args:
            template_path: Path to the template JSON file
            output_filename: Optional output filename

        Returns:
            Path to the generated video file
        u   🎬 Processing template: r�   zutf-8)�encodingNzFailed to load template: zTemplate validation failed�widthi�  �heighti   �
output_format�mp4z_unified_output.r   �elements�type�compositionu   ✅ Processed scene: �name�Unknown�
 - Duration: �.2f�sz!No valid scenes found in templateu   🔗 Concatenating z
 scenes...�compose)�methodu   💾 Writing final video to r_   r#   �libx264�aacztemp-audio.m4aTF)�fps�codec�audio_codec�temp_audiofile�remove_temp�verboser3   u    🎉 Video processing complete: u   📊 Total duration: )r3   r4   rl   ra   �loadrp   ro   �validate_templater�   �getr   �stemr   �preprocess_template�
process_scenerH   r�   r5   r   �write_videofilern   �close)r6   r�   r�   ry   �
template_datar{   r�   r�   r�   �
template_name�output_path�processed_template�scene_clips�total_duration�element�
scene_clip�final_video�clips                     r7   �process_templatez&UnifiedVideoProcessor.process_template9  s�  � �� � 	���0���@�A�	��m�S�7�;� 
-�q� $�	�	�!��
�
-� �%�%�m�4��9�:�:� �!�!�'�3�/���"�"�8�T�2��%�)�)�/�5�A�
�� ��/�4�4�M�!.��/?�
��O�O��o�o��7�� $(�#;�#;�M�#J�J�� ����)�-�-�j�"�=� 	A�G��{�{�6�"�m�3�#'�#5�#5�g�u�f�#M�M�
���&�&�z�2�"�j�&9�&9�9�N��K�K�"7����F�I�8V�7W�Wd�eo�ex�ex�y|�d}�}~� �  A�
	A� ��@�A�A� 	���)�#�k�*:�);�:�F�G�,�[��K�� 	���2�;�-�s�C�D��#�#�������+���� 	$� 		
� 	����� 	�D��J�J�L�	� 	���6�{�m�D�E����+�N�3�+?�q�A�B��;���A
-� 
-��� 	��L�L�4�Q�C�8�9���	��( K�� N�sk   �J�I- �I � I- �BJ�J�1J�J�$J�%J�,D4J� I*�%I- �-	J�6J�J�J�Jr�   c                 ��  � 	 t        |t        �      st        j                  d�       yd|vrt        j                  d�       y|d   }t        |t        �      st        j                  d�       y|D �cg c]  }|j                  d�      dk(  s�|�� }}|st        j                  d�       yt        j
                  d	�       y
c c}w # t        $ r"}t        j                  d|� ��       Y d}~yd}~ww xY w)
zValidate template structurezTemplate must be a dictionaryFr�   z!Template must have 'elements' keyz"Template 'elements' must be a listr�   r�   z3Template must have at least one composition elementu   ✅ Template validation passedTzTemplate validation error: N)r�   �dictr3   ro   �listr�   r4   rp   )r6   r�   r�   r{   �compositionss        r7   r�   z'UnifiedVideoProcessor.validate_template�  s�   � �	��m�T�2����<�=����.����@�A��$�Z�0�H��h��-����A�B�� (0�R�!�1�5�5��=�M�3Q�A�R�L�R�����R�S���K�K�8�9���
 S�� � 	��L�L�6�q�c�:�;���	�sF   �%C �C �*C �-C �1B?�B?�C �)C �?C �	C/�
C*�*C/c              �   �   K  � t         j                  d�       |j                  �       }| j                  |j	                  dg �      �      � d{  ���  |S 7 ��w)z�
        Preprocess template to generate audio and prepare assets

        Args:
            template_data: Template data dictionary

        Returns:
            Processed template data with audio paths
        u   🔄 Preprocessing template...r�   N)r3   r4   �copy�_preprocess_elementsr�   )r6   r�   r�   s      r7   r�   z)UnifiedVideoProcessor.preprocess_template�  sT   � �� � 	���4�5�*�/�/�1�� �'�'�(:�(>�(>�z�2�(N�O�O�O�!�!� 	P�s   �A
A�A�
Ar�   c              �   �L  K  � |D ]�  }|j                  d�      }|dk(  r,|j                  dg �      }| j                  |�      � d{  ���  �E|dk(  r| j                  |�      � d{  ���  �d|dk(  s�j|j                  dd�      s�}| j                  |�      � d{  ���  �� y7 �Y7 �<7 ��w)	z1Recursively preprocess elements to generate audior�   r�   r�   N�audiorS   �generate_audioF)r�   r�   �_preprocess_audio_element�_preprocess_text_audio)r6   r�   r�   �element_type�nested_elementss        r7   r�   z*UnifiedVideoProcessor._preprocess_elements�  s�   � �� �� 	?�G�"�;�;�v�.�L��}�,�")�+�+�j�"�"=���/�/��@�@�@���(��4�4�W�=�=�=���'��;�;�/��7��5�5�g�>�>�>�	?� A�� >��
 ?�sB   �AB$�B�B$�#B �$
B$�/B$�B$�B"�B$� B$�"B$�
audio_elementc              �   �  K  � |j                  dd�      }|rt        j                  j                  |�      s�|j                  d|j                  dd�      �      }|ryt        j                  d|dd � d��       | j
                  |�      � d{  ��� }|r#||d<   d	|d
<   t        j                  d|� ��       y| j                  |�      }|r||d<   d	|d
<   yyyy7 �J�w)z5Preprocess audio element to generate speech if needed�sourcer�   rS   �
transcriptu*   🎤 Generating speech for audio element: Nr^   r_   T�	generatedu   ✅ Audio generated: )r�   r&   �pathrB   r3   r4   r|   r�   )r6   r�   r�   �text_contentrs   �
fallback_paths         r7   r�   z/UnifiedVideoProcessor._preprocess_audio_element�  s�   � �� ��"�"�8�R�0�� �R�W�W�^�^�F�3�(�,�,�V�]�5F�5F�|�UW�5X�Y�L�����H��VY�WY�IZ�H[�[^�_�`� $(�#>�#>�|�#L�L�
��.8�M�(�+�15�M�+�.��K�K�"7�
�|� D�E� %)�$A�$A�,�$O�M�$�2?�
�h�/�59�
�k�2� %� �	 4� M�s   �BC�
C�AC�text_elementc              �   ��   K  � |j                  dd�      }|r[t        j                  d|dd � d��       | j                  |�      � d{  ��� }|r#||d<   d|d	<   t        j                  d
|� ��       yyy7 �+�w)z,Generate audio for text element if requestedrS   r�   u)   🎤 Generating speech for text element: Nr^   r_   �audio_sourceT�	has_audiou   ✅ Text audio generated: )r�   r3   r4   r|   )r6   r�   r�   rs   s       r7   r�   z,UnifiedVideoProcessor._preprocess_text_audio�  s�   � �� �#�'�'���3����K�K�C�L�QT�RT�DU�CV�VY�Z�[�#�:�:�<�H�H�J��/9��^�,�,0��[�)����8���E�F� � � I�s   �AA5�A3�,A5�
scene_elementr�   r�   c              �   �P  K  � |j                  dd�      }t        j                  d|� ��       |j                  dd�      }|j                  dg �      }|st        j                  d|� d��       y	|D �cg c]  }|j                  d
�      dk(  s�|�� }}|D �cg c]  }|j                  d
�      dk(  s�|�� }	}|D �cg c]  }|j                  d
�      d
k(  s�|�� }
}|D �cg c]  }|j                  d
�      dk(  s�|�� }}|D �cg c]  }|j                  d
�      dk(  s�|�� }}| j	                  ||||�      }
g }|	D ]*  }| j                  ||||�      }|s�|j
                  |�       �, g }|D ]*  }| j                  ||||�      }|s�|j
                  |�       �, g }|D ]1  }| j                  |||�      � d	{  ��� }|s�!|j
                  |�       �3 d	}|
r| j                  |
d   �      }|
g|z   |z   |z   }|D �cg c]  }|��|��	 }}|st        j                  d|� d��       y	t        |�      dk(  r|d   }nt        |||f��      }|j                  |�      }|r|j                  |�      }t        j                  d|� d|� d��       |S c c}w c c}w c c}w c c}w c c}w 7 ��c c}w �w)z�
        Process a scene/composition element

        Args:
            scene_element: Scene element data
            width: Canvas width
            height: Canvas height

        Returns:
            Composed video clip or None
        r�   z
Unknown Sceneu   🎬 Processing scene: r�   r   r�   zScene 'z' has no elementsNr�   �imagerS   r�   �shaper�   r   zNo valid clips in scene '�'r   )�sizeu   ✅ Scene processed: r�   r�   )r�   r3   r4   rC   �create_background_clip�create_text_cliprH   �create_shape_clipr�   �create_audio_clipr5   r   r�   �	set_audio)r6   r  r�   r�   �
scene_name�scene_durationr�   r{   �image_elements�
text_elements�audio_elements�shape_elements�composition_elements�background_clip�
text_clipsr�   �	text_clip�shape_clips�
shape_element�
shape_clip�composition_clips�composition_element�composition_clip�
audio_clip�video_clipsr�   �composite_clips                              r7   r�   z#UnifiedVideoProcessor.process_scene�  s�  � �� � #�&�&�v��?�
����-�j�\�:�;� '�*�*�:�s�;�� �$�$�Z��4����N�N�W�Z�L�0A�B�C�� &.�J�����v��'�1I�!�J��J�$,�H�q����f�
��0G��H�
�H�%-�J�����v��'�1I�!�J��J�%-�J�����v��'�1I�!�J��J�+3�V�a�q�u�u�V�}�
�7U��V��V� �5�5�n�e�V�Uc�d�� �
�)� 	-�L��-�-�l�E�6�>�Z�I���!�!�)�,�	-� ��+� 	/�M��/�/�
�u�f�n�]�J���"�"�:�.�	/� ��#7� 	;��%)�%7�%7�8K�U�TZ�%[�[���!�(�(�)9�:�	;� �
���/�/��q�0A�B�J� '�'�+�5�
�B�EV�V�� )4�H��t�7G�t�H��H���N�N�6�z�l�!�D�E�� �{��q� �(��^�N�/��5�&�/�R�N� (�4�4�^�D�� �+�5�5�j�A�N����+�J�<�}�^�DT�TU�V�W����w K��H��J��J��V�,  \�� I�s�   �A/J&�1J�J�J&�J�/J�3J&�9J�J�J&�J�7J�;J&�J�J�3J&�0J&�0J&�4J�5J&�<;J&�7J!�?J!�BJ&�!J&r  r�   c                 �F  � |�r]|d   }| j                  |j                  dd�      �      }|�r4|j                  �       �r#t        j	                  d|� ��       t        j                  |�      }|j                  ||ft
        j                  j                  �      }|j                  d�      }|r\| j                  |�      }	t        j                  d||f|	t        d�      fz   �      }
t        j                  |j                  d�      |
�      }t        j                   |j                  d�      �      }t#        ||�	�      }|j                  d
g �      }
|
r| j%                  ||
||�      }|S t        j	                  d�       | j                  | j&                  �      }t)        ||f||��      }|S )
z9Create background clip from image elements or solid colorr   r�   r�   u   📸 Using background image: �
color_overlay�RGBAg      S@�RGB�r�   �
animationsu$   🎨 Creating solid color background)r  �colorr�   )�resolve_image_sourcer�   rB   r3   r4   r   rl   �resize�
Resampling�LANCZOSr�   �newr�   �alpha_composite�convert�np�arrayr   �apply_image_animationsr0   r   )r6   r  r�   r�   r�   �
image_element�
image_path�imgr!  �
overlay_color�overlay�	img_arrayr  r%  �background_colors                  r7   r  z,UnifiedVideoProcessor.create_background_clipK  s�  � � �*�1�-�M��2�2�=�3D�3D�X�r�3R�S�J��j�/�/�1����;�J�<�H�I� �j�j��,���j�j�%���%�2B�2B�2J�2J�K�� !.� 1� 1�/� B�
� �$(�$4�$4�]�$C�M�#�i�i������RU�V_�R`�Qb�Ab�c�G��/�/����F�0C�W�M�C� �H�H�S�[�[��%7�8�	� #,�I��"I�� +�.�.�|�R�@�
��&*�&A�&A�/�S]�_d�fl�&m�O�&�&� 	���:�;��+�+�D�,I�,I�J��#�%���@P�[c�d���r9   r�   c                 �  � |s| j                  �       S t        |�      }|j                  �       r|S | j                  |z  }|j                  �       r|S dD ])  }| j                  |� |� �z  }|j                  �       s�'|c S  	 t	        |�      }| j                  |�      S # t        $ r Y nw xY wt        j                  d|� d��       | j                  �       S )z�
        Resolve image source to actual file path

        Args:
            source: Image source (could be ID, filename, or path)

        Returns:
            Path to image file or None
        )r?   rA   r@   r>   z Could not resolve image source 'z', using random image)	rO   r   rB   r   r�   rR   r�   r3   rC   )r6   r�   �source_path�images_path�ext�	test_pathrP   s          r7   r'  z*UnifiedVideoProcessor.resolve_image_sourceu  s�   � � ��(�(�*�*� �6�l�������� �o�o��.�������� 5� 	!�C����V�H�S�E�*:�:�I����!� � �	!�	���K�E��*�*�5�1�1��� 	��	�� 	���9�&��AV�W�X��$�$�&�&s   � B �	B(�'B(r�   r%  c                 ��  ��	�
� |D ]�  }|j                  dd�      }|dk(  re| j                  |j                  dd�      ||�      dz  �
| j                  |j                  dd�      ||�      dz  �	��	�
fd	�}�j                  |�      ��|d
k(  s��|j                  dd�      }|j                  d
d�      r�j                  |�      �|j                  dd�      s�͉j	                  |�      ��� �S )zApply animations to image clipr�   r�   �pan�start_scalez100%r�   �	end_scalez120%c                 �<   �� | �j                   z  }���z
  |z  z   }|S �Nr$  )�t�progress�scaler�   r@  r?  s      ���r7   �resize_funczAUnifiedVideoProcessor.apply_image_animations.<locals>.resize_func�  s*   �� � �4�=�=�0�H�'�9�{�+B�h�*N�N�E� �Lr9   �fader�   r[   �fade_inF�fade_out)r�   r�   r(  �fadein�fadeout)r6   r�   r%  r�   r�   �	animation�	anim_typerF  �
fade_durationr@  r?  s    `       @@r7   r0  z,UnifiedVideoProcessor.apply_image_animations�  s�   �� � $� 	7�I�!�
�
�f�b�1�I��E�!�"�2�2�9�=�=��PV�3W�Y^�`f�g�jm�m�� �0�0����{�F�1S�UZ�\b�c�fi�i�	�!�
 �{�{�;�/���f�$� )�
�
�j�#� >�
��=�=��E�2��;�;�}�5�D��=�=��U�3��<�<�
�6�D�-	7�0 �r9   c                 �J  � |j                  dd�      }|sy|j                  dd�      }|j                  dd�      }| j                  |||�      }|j                  d| j                  �      }	|j                  d	d
�      }
| j                  |	�      }| j                  |
�      }t        j                  d|dd � d
��       	 t
        |||||d��      j                  |�      }
| j                  |j                  dd�      |d�      }| j                  |j                  dd�      |d�      }|
j                  ||f�      }
|j                  dg �      }|r| j                  |
|�      }
t        j                  d|� d|� d��       |
S # t        $ r"}t        j                  d|� ��       Y d}~yd}~ww xY w)z"Create text clip from text elementrS   zSample TextN�font_family�Arial�	font_size�24px�
fill_color�stroke_colorr%   u   📝 Creating text clip: �   r_   r   )�fontsizer&  �fontrU  �stroke_width�x�50%r   �yr%  u#   ✅ Text clip created at position (�, r�   zFailed to create text clip: )
r�   r�   r/   r�   r3   r4   r   r�   r�   �set_position�apply_text_animationsrp   ro   )r6   r�   r�   r�   r�   r�   rP  �
font_size_strrR  rT  rU  �fill_rgb�
stroke_rgbr  �x_pos�y_posr%  r{   s                     r7   r	  z&UnifiedVideoProcessor.create_text_clip�  s�  � � $�'�'��
�>���� #�&�&�}�g�>��$�(�(��f�=�
��(�(���v�F�	�!�%�%�l�D�4K�4K�L�
�#�'�'��	�B�� �#�#�J�/���%�%�l�3�
����/��S�b�0A�/B�#�F�G�	� ��"�� �'��
� �l�8�$� 
� �'�'��(8�(8��e�(D�e�Q�O�E��'�'��(8�(8��e�(D�f�a�P�E�!�.�.��u�~�>�I� &�)�)�,��;�J�� �6�6�y�*�M�	��K�K�=�e�W�B�u�g�Q�O�P����� 	��L�L�7��s�;�<���	�s   �:B<E7 �7	F"� F�F"c                 �&  � |D ]�  }|j                  dd�      }|dk(  rZ|j                  dd�      }|j                  dd�      r|j                  |�      }|j                  dd�      s�b|j                  |�      }�t|d	k(  s�z|j                  d
d�      }�� |S )zApply animations to text clipr�   r�   rG  r�   r[   rH  FrI  �slide�	direction�up)r�   rJ  rK  )r6   r�   r%  rL  rM  rN  rg  s          r7   r_  z+UnifiedVideoProcessor.apply_text_animations�  s�   � � $� 	�I�!�
�
�f�b�1�I��F�"� )�
�
�j�#� >�
��=�=��E�2��;�;�}�5�D��=�=��U�3��<�<�
�6�D��g�%�%�M�M�+�t�<�	��	�  �r9   r  c                 �@  � t         j                  d|j                  dd�      � ��       |j                  dd�      }|j                  dd�      }|j                  dd�      }| j                  |||�      }| j                  |||�      }	| j	                  |||	|�      }
|
�y	t        |
|�
�      }| j
                  |j                  dd�      ||�      }| j
                  |j                  d
d�      ||	�      }
|j                  ||
f�      }t         j                  d|� d|
� d��       |S )zCreate shape overlay clipu   🔷 Creating shape clip: �id�unknownrT  r$   r�   �100pxr�   Nr$  rZ  r[  r\  u$   ✅ Shape clip created at position (r]  r�   )r3   r4   r�   r�   �create_shape_imager   r�   r^  )r6   r  r�   r�   r�   rT  �shape_width�shape_height�shape_width_px�shape_height_px�shape_imager  rc  rd  s                 r7   r
  z'UnifiedVideoProcessor.create_shape_clip  s6  � � 	���0��1B�1B�4��1S�0T�U�V� #�&�&�|�Y�?�
�#�'�'���9��$�(�(��7�;�� �-�-�k�5�&�I���.�.�|�U�F�K�� �-�-��>�?�J�
�� ��� �{�X�>�
� �#�#�M�$5�$5�c�5�$A�5�.�Y���#�#�M�$5�$5�c�5�$A�6�?�[���,�,�e�U�^�<�
����:�5�'��E�7�!�L�M��r9   rT  c                 �<  � 	 | j                  |�      }t        j                  d||fd�      }t        j                  |�      }|j                  d�      }|j                  dd�      }	|r|j
                  dd||f|dz   ��       nZ|	d	k(  r|j                  dd||f|dz   ��       n:|	d
k(  r|j                  dd||f|dz   ��       n|j
                  dd||f|dz   ��       t        j                  |�      S # t        $ r"}
t        j                  d|
� ��       Y d}
~
yd}
~
ww xY w)
zCreate shape imager"  )r   r   r   r   r�   �
shape_type�	rectangler   )r�   )�fill�circle�ellipsezFailed to create shape image: N)
r�   r   r+  r   �Drawr�   ru  rx  r.  r/  rp   r3   ro   )r6   r  r�   r�   rT  ra  r3  �drawr�   rt  r{   s              r7   rm  z(UnifiedVideoProcessor.create_shape_image#  s%  � �	��'�'�
�3�H� �)�)�F�U�F�O�\�B�C��>�>�#�&�D� !�$�$�V�,�D�&�*�*�<��E�J�� ����1�e�V�4�8�f�;L��M��x�'����a��E�6�2��F�9J��K��y�(����a��E�6�2��F�9J��K� ����1�e�V�4�8�f�;L��M��8�8�C�=� ��� 	��L�L�9�!��=�>���	�s   �C-C0 �0	D�9D�Dc                 �z  � |j                  dd�      }|rt        j                  j                  |�      st        j                  d|� ��       y	 t        j
                  d|� ��       t        |�      }|j                  dd�      }|dk7  r|j                  |�      }|j                  dd	�      }|j                  d
d	�      }|d	kD  r|j                  |�      }|d	kD  r|j                  |�      }t        j
                  d|j                  d�d
��       |S # t        $ r"}t        j                  d|� ��       Y d}~yd}~ww xY w)z$Create audio clip from audio elementr�   r�   zAudio source not found: Nu   🎵 Loading audio: �volumer[   �
audio_fade_inr   �audio_fade_outu   ✅ Audio clip loaded: r�   r�   zFailed to load audio: )r�   r&   r�   rB   r3   rC   r4   r   �volumexrJ  rK  r�   rp   ro   )r6   r�   r�   r  r|  rH  rI  r{   s           r7   r  z'UnifiedVideoProcessor.create_audio_clipF  s.  � � �"�"�8�R�0���R�W�W�^�^�F�3��N�N�5�f�X�>�?��	��K�K�.�v�h�7�8�&�v�.�J� #�&�&�x��5�F���}�'�/�/��7�
� $�'�'���;�G�$�(�(�)9�1�=�H���{�'�.�.�w�7�
��!�|�'�/�/��9�
��K�K�1�*�2E�2E�c�1J�!�L�M����� 	��L�L�1�!��5�6���	�s   �C D �	D:�D5�5D:c                 �L  � 	 | j                   j                  d�      D ]  }|j                  �        � | j                   j                  d�      D ]  }|j                  �        � t        j	                  d�       y# t
        $ r"}t        j
                  d|� ��       Y d}~yd}~ww xY w)zClean up temporary filesz*.mp3z*.wavu   🧹 Temporary files cleaned upz$Failed to clean up temporary files: N)r+   �glob�unlinkr3   r4   rp   rC   )r6   �	temp_filer{   s      r7   �cleanupzUnifiedVideoProcessor.cleanuph  s�   � �	G�!�]�]�/�/��8� 
#�	�� � �"�
#�!�]�]�/�/��8� 
#�	�� � �"�
#��K�K�9�:��� 	G��N�N�A�!��E�F�F��	G�s   �A5A8 �8	B#�B�B#)�zC:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140N�unified_outputs)�defaultrB  )-�__name__�
__module__�__qualname__�__doc__rn   r8   r   r   r1   r   rO   r�   rR   r|   r�   r   r�   r�   r�   r�   r�   r   �boolr�   r�   r�   r�   r�   r   r�   r�   r  r'  r   r0  r   r	  r_  r
  r.  �ndarrayrm  r   r  r�  � r9   r7   r   r   <   s  � �� $a�'+�#4�$<� �$<�!$�$<� !�$<�L��T�
� � 4�(�4�.� 4�,�� ,���� ,�9�� 9�� 9�T\�]`�Ta� 9�v�S� �X�c�]� �:�S� �U�3��S�=�-A� �6�C� �E�#�s�C�-�,@� ��S� �� �TW� �\_� �8F�3� F�#� F�Z]� F�be� F�6O �C� O �#� O �Y\� O �b�t� �� �<"�t� "�� "�&?�4��:� ?�&:�T� :�4G�� G�S�� S�c� S�3� S�S[�\i�Sj� S�j(�T�$�Z� (�� (�UX� (�di� (�n{� (�T&'�3� &'�8�D�>� &'�P�9� �$�t�*� �UX� �be� �js� �:0�T� 0�#� 0�s� 0�V[� 0�`h�iq�`r� 0�d�(� ��T�
� �x� �* �t�  �C�  ��  �X]�  �bj�kt�bu�  �D!�� !�S� !�#� !�[^� !�ck�ln�lv�lv�cw� !�F �t�  ���8O�  �D
Gr9   r   �	processor�
templates_dirc           	   �   �  K  � t        |�      }|j                  �       st        j                  d|� ��       yt	        |j                  d�      �      }t        j
                  dt        |�      � d��       g }|D ]�  }	 t        j
                  dd� ��       t        j
                  d|j                  � ��       t        j
                  d� �       | j                  t        |�      �      � d{  ��� }|j                  |j                  d	|d
��       t        j
                  d|j                  � ��       �� t        j
                  dd� ��       t        j
                  d�       t        j
                  d� �       |D �cg c]
  }|d   d	k(  s�|�� }	}|D �cg c]
  }|d   dk(  s�|�� }
}t        j
                  dt        |	�      � ��       t        j
                  dt        |
�      � ��       |	r=t        j
                  d�       |	D ]#  }t        j
                  d|d   � d|d   � ��       �% |
r=t        j
                  d�       |
D ]#  }t        j
                  d|d   � d
|d   � ��       �% |S 7 ��}# t        $ rX}t        j                  d|j                  � d
|� ��       |j                  |j                  dt        |�      d��       Y d}~��Md}~ww xY wc c}w c c}w �w)z0Process all templates in the templates directoryzTemplates directory not found: Nz*.jsonu   🎬 Found z templates to process�
z<============================================================u   🎯 Processing template: �success)�templaterj   �outputu   ✅ Successfully processed: u   ❌ Failed to process z: �failed)r�  rj   ro   u   📊 PROCESSING SUMMARYrj   u   ✅ Successful: u   ❌ Failed: u'   
🎉 Successfully processed templates:z  - r�  u    → r�  u   
💥 Failed templates:ro   )
r   rB   r3   ro   r�   r�  r4   r5   r�   r�   rn   rH   rp   )r�  r�  �templates_path�template_files�results�
template_filer�   r{   r�   �
successfulr�  �results               r7   �process_all_templatesr�  v  s�  � �� � �-�(�N�� � �"����6�~�6F�G�H���.�-�-�h�7�8�N�
�K�K�+�c�.�1�2�2G�H�I��G�'� �
�	��K�K�"�V�H�
�&��K�K�4�]�5G�5G�4H�I�J��K�K�6�(�$� )� :� :�3�}�;M� N�N�K��N�N�)�.�.�#�%�� 
� 
�K�K�6�}�7I�7I�6J�K�L��0 �K�K�"�V�H�
��
�K�K�)�+�
�K�K�6�(��$�A���(��y�(@�!�A�J�A� �
<�A�A�h�K�8�$;�a�
<�F�
<�
�K�K�"�3�z�?�"3�4�5�
�K�K�,�s�6�{�m�,�-�����>�@� � 	L�F��K�K�$�v�j�1�2�%��x�8H�7I�J�K�	L� ����.�0�� 	H�F��K�K�$�v�j�1�2�"�V�G�_�4E�F�G�	H� �N�M O�� � 	��L�L�1�-�2D�2D�1E�R��s�K�L��N�N�)�.�.�"��Q��� 
� 
��	�� B��
<�sk   �A8K�;A-I(�(I%�)AI(�.A	K�7
K�K�	K�
K�K�!CK�%I(�(	K	�1A
K�>K�K	�	
Kc               �   �  K  � ddl } | j                  d��      }|j                  ddd��       |j                  d	d
d��       |j                  d
dd��       |j                  dd��       |j                  ddd��       |j                  ddd��       |j                  ddd��       |j                  �       }t	        |j
                  |j                  |j                  ��      }	 |j                  r4t        j                  d�       t        ||j                  �      � d{  ���  n�|j                  rit        j                  d|j                  � ��       |j                  |j                  |j                  �      � d{  ��� }t        j                  d |� ��       n"|j!                  �        	 |j#                  �        y|j#                  �        y7 ��7 �R# t$        $ r}t        j'                  d!|� ��       � d}~ww xY w# |j#                  �        w xY w�w)"zMain execution functionr   Nz*Unified Video Processor for VisionFrame AI)�descriptionr�  �?zTemplate file to process)�nargs�helpz--outputz-ozOutput filename)r�  z--images-dirr�  zDirectory containing images)r�  r�  z--runway-keyz$Runway API key for speech generationz--output-dirr�  zOutput directoryz--batch�
store_truez,Process all templates in templates directory)�actionr�  z--templates-dir�	templatesz(Templates directory for batch processing)r   r   r   u2   🚀 Starting batch processing of all templates...u!   🚀 Processing single template: u!   🎉 Video created successfully: u   💥 Processing failed: )�argparse�ArgumentParser�add_argument�
parse_argsr   r   �
runway_keyr   �batchr3   r4   r�  r�  r�  r�   r�  �
print_helpr�  rp   ro   )r�  �parser�argsr�  r�   r{   s         r7   �mainr�  �  s�  � �� ��
�
$�
$�1]�
$�
^�F�
���
�#�4N��O�
���
�D�/@��A�
����  1n�9� � ;�
����-S��T�
����0A�HZ��[�
���	�,�=k��l�
���)�;�Eo��p�����D� &��?�?�����?�?��I���:�:��K�K�L�M�'�	�4�3E�3E�F�F�F�
�]�]��K�K�;�D�M�M�?�K�L� )� :� :�4�=�=�$�+�+� V�V�K��K�K�;�K�=�I�J� 
����� 	����	����' 
G��
 W�� � ����/��s�3�4�
���� 	����s[   �CH�!:G
 �G	�AG
 �8G�9-G
 �'"H�	G
 �G
 �
	G4�G/�/G4�4G7 �7H	�	H�__main__)r�  ):r�  r&   �sysra   �loggingrM   �requestsr)   �asynciorg   rd   �ImportError�print�pathlibr   �typingr   r   r   r   r   r	   r
   �numpyr.  �PILr   r   r
   �cv2�moviepy.editorr   r   r   r   r   r   r   �moviepy.video.io.VideoFileClip�"moviepy.video.io.ImageSequenceClipr   �moviepy.video.VideoClip�,moviepy.video.compositing.CompositeVideoClip�moviepy.audio.io.AudioFileClip�%moviepy.video.compositing.concatenate�exit�basicConfig�INFO�FileHandler�
StreamHandler�	getLoggerr�  r3   r   rn   r�  r�  �runr�  r9   r7   �<module>r�     s�  ���
 
� 
� � � 
� � � �[���� � :� :� � � +� +� 
��� � � 
�
)�*�  �� � �
�,�,�6�����:�;�������� 
��	�	�8�	$��vG� vG�t9�+@� 9�QT� 9�x.�b �z���G�K�K���� ��c � [���	�
Y�Z�[��" � ��@�H�?�S�@�P�5�
�0�1��� �
�A�B����������sG   �C9 �D �9D�
D�E1�4E	�E1�	E+�(E1�*E+�+E1�0E1